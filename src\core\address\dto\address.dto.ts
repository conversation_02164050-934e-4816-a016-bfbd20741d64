import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/dto/base.dto';

export class AddressDto extends EntityDto {
  @ApiProperty({
    name:'latitude',
    type: String,
    required: true,
    description: 'The latitude of the address'
  })
  latitude?: string;

  @ApiProperty({
    name:'longitude',
    type: String,
    required: true,
    description: 'The longitude of the address'
  })
  longitude?: string;

  @ApiProperty({
    name:'houseAddress',
    type: String,
    required: false,
    description: 'The house address of the user'
  })
  houseAddress?: string;

  @ApiProperty({
    name:'country',
    type: String,
    required: false,
    description: 'The country of the address'
  })
  country?: string;

  @ApiProperty({
    name:'state',
    type: String,
    required: true,
    description: 'The state of the address'
  })
  state?: string;

  @ApiProperty({
    name:'postalCode',
    type: String,
    required: false,
    description: 'The postal code of the address'
  })
  postalCode?: string;
}
