import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';

async function bootstrap() {
  const logger = new Logger('Application');
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true, // Automatically transform payloads to DTOs
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );
  app.enableCors(); // Enable CORS
  app.setGlobalPrefix('api/'); // Add a global prefix to all routes
  app.useGlobalFilters(new HttpExceptionFilter()); // Add global exception filter

  // Enable URI-based versioning
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // Get configuration service for environment variables
  const configService = app.get(ConfigService);

  // Swagger configuration
  const swaggerConfig = new DocumentBuilder()
    .setTitle('Green Pasture Backend System')
    .setDescription('This is a RESTful web service for the Green application')
    .setVersion('1.0.0')
    // .addServer('http://localhost:3000', 'Local development server')
    // .addServer('http://localhost:3000', 'Staging server')
    // .addServer('https://api.greenpasture.com', 'Production server')
    .setContact(
      'Support Team',
      'https://greenpasture.com/support',
      '<EMAIL>',
    )
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api-docs', app, document);

  // Serve Swagger JSON at a specific endpoint
  app.getHttpAdapter().get('/api-docs-json', (req, res) => {
    res.json(document);
  });

  // Log and start the application
  const port = configService.get<string>('port') || 3000; // Default to 3000 if no port is specified
  logger.log(`Application running on port ${port}`);
  await app.listen(port);
}

// Start the application
bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('Error starting the application:', error);
});
