import { MigrationInterface, QueryRunner, TableColumn, TableIndex } from 'typeorm';

export class ModifyPermissionTable1749587428724 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('permission', 'IDX_PERMISSION');
    await queryRunner.dropColumn('permission', 'status');
    await queryRunner.addColumn(
      'permission',
      new TableColumn({
        name: 'status',
        type: 'varchar',
      }),
    );

    await queryRunner.createIndex(
      'permission',
      new TableIndex({
        name: 'IDX_PERMISSION',
        columnNames: ['name', 'description', 'status', 'created_at', 'created_by', 'updated_at', 'updated_by'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('permission', 'IDX_PERMISSION');
    await queryRunner.dropColumn('permission', 'status');
    await queryRunner.addColumn(
      'permission',
      new TableColumn({
        name: 'status',
        type: 'enum',
        enum: ['A', 'I'],
        default: "'A'",
      }),
    );

    await queryRunner.createIndex(
      'permission',
      new TableIndex({
        name: 'IDX_PERMISSION',
        columnNames: ['name', 'description', 'status', 'created_at', 'created_by', 'updated_at', 'updated_by'],
      }),
    );
  }
}
