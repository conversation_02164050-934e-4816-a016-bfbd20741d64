import { MigrationInterface, QueryRunner, TableColumn, TableIndex } from 'typeorm';

export class ModifyRoleTable1749587444036 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('role', 'IDX_ROLE');
    await queryRunner.dropColumn('role', 'status');
    await queryRunner.addColumn(
      'role',
      new TableColumn({
        name: 'status',
        type: 'varchar',
      }),
    );

    await queryRunner.createIndex(
      'role',
      new TableIndex({
        name: 'IDX_ROLE',
        columnNames: ['name', 'description', 'status', 'created_at', 'created_by', 'updated_at', 'updated_by'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('role', 'IDX_ROLE');
    await queryRunner.dropColumn('role', 'status');
    await queryRunner.addColumn(
      'role',
      new TableColumn({
        name: 'status',
        type: 'enum',
        enum: ['A', 'I'],
        default: "'A'",
      }),
    );

    await queryRunner.createIndex(
      'role',
      new TableIndex({
        name: 'IDX_ROLE',
        columnNames: ['name', 'description', 'status', 'created_at', 'created_by', 'updated_at', 'updated_by'],
      }),
    );
  }
}
