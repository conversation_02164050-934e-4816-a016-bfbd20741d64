import { SeedRoleDto } from '@core/authorization/role/dto/seed-role.dto';
import { CoreConstants } from '@common/utils/core.constants';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { CoreUtils } from '@common/utils/core.utils';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { SeedProfileDto } from '@core/profile/dto/seed-profile.dto';

export const roles: Array<SeedRoleDto> = [
  { name: CoreConstants.SUPER_ADMIN_ROLE, description: 'Super Admin role.' },
];

export const accounts: Array<SeedProfileDto> = [
  {
    firstName: 'Abayomi',
    lastName: null,
    accountType: ProfileType.STAFF,
    email: '<EMAIL>',
    password: CoreUtils.hashPassword('P@ssW0rd'),
    phoneNumber: '+*************',
    role: {
      name: CoreConstants.SUPER_ADMIN_ROLE,
      description: 'Super Admin role.',
      permissions: [
        { name: UserActions.MANAGE, description: 'Can Manage Record' },
      ],
    }
  }
]
