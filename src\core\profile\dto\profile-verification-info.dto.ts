import { ApiProperty } from "@nestjs/swagger";

export class ProfileVerificationInfoDto {
  @ApiProperty({
    name: 'kycVerified',
    type: Boolean,
    required: false,
    description: 'The KYC verification status of the account'
  })
  kycVerified: boolean;

  @ApiProperty({
    name: 'verified',
    type: Boolean,
    required: false,
    description: 'The verification status of the account'
  })
  verified: boolean;
}
