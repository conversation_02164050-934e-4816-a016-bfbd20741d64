
import { SetMetadata } from '@nestjs/common';
import { Subjects } from '../../core/authorization/casl-ability-factory/casl-ability-factory.service';
import { UserActions } from "../enumerations/user_actions.enum";

export interface RequiredRule {
  action: UserActions;
  subject: Subjects;
}

export const CHECK_ABILITY = 'check_ability';
// Custom decorator to set action and subject metadata
export const CheckAbilities = (...requirements: Array<RequiredRule>) =>
  SetMetadata(CHECK_ABILITY, requirements);
