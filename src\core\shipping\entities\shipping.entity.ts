import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, JoinColumn, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Order } from '@core/order/entities/order.entity';
import { Address } from '@core/address/entities/address.entity';

@Entity({ name: 'shipping_info' })
export class Shipping extends AbstractEntity{

  @AutoMap()
  @Column({ name: 'carrier', type: 'varchar', length: 50 })
  carrier: string; // e.g., FedEx, UPS, DHL

  @AutoMap()
  @Column({ name: 'tracking_number', type: 'varchar', length: 50 })
  trackingNumber: string; // There's already a function to generate a tracking number. Check CoreUtils for that.

  @AutoMap()
  @Column({ name: 'shipping_cost', type: 'decimal', precision: 10, scale: 2 })
  shippingCost: number; // Cost of shipping

  @AutoMap()
  @Column({ name: 'estimated_delivery_date', type: 'timestamp' })
  estimatedDeliveryDate: Date; // Estimated delivery date for the shipment

  @AutoMap()
  @Column({ name: 'shipping_date', type: 'timestamp' })
  shippingDate: Date; // Date when the item was shipped

  @AutoMap(() => Address)
  @OneToOne(() => Address, { eager: true })
  @JoinColumn({ name: 'address_id', referencedColumnName: 'id' })
  shippingAddress: Address; // Address where the item is shipped

  @AutoMap(() => Order)
  @OneToOne(() => Order, { eager: true })
  @JoinColumn({ name: 'order_id', referencedColumnName: 'id' })
  product: Order;  // Order associated with the shipment
}
