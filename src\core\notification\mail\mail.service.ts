import { Injectable } from '@nestjs/common';
import { join } from 'path';
import { InjectMailer, Mailer, template } from 'nestjs-mailer';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '@common/logger/logger.service';

@Injectable()
export class MailService {
  from: string;

  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
    @InjectMailer() private readonly mailer: Mailer,
  ) {
    this.from = this.configService.get<string>('mails.defaults.from');
    this.logger.setContext(MailService.name);
  }

  /**
   * Send SMTP mail using the default mailer configuration in the application.
   * @param recipient - The recipient of the mail.
   * @param subject - The subject of the mail.
   * @param message - The message to be sent.
   */
  async sendSmtpMail(recipient: string, subject: string, message: any) {
    try {
      await this.mailer.sendMail({
        to: recipient,
        from: `"Green Pastures Team" <${this.from}>`,
        subject: subject,
        text: message,
      });
      this.logger.log(`Mail sent to ${recipient}`);
    } catch (err) {
      this.logger.error(`Error sending mail: \n${err?.message}`);
    }
  }

  /**
   * Send SMTP mail using a template.
   * The template is rendered using the context provided.
   * @param recipient - The recipient of the mail.
   * @param subject - The subject of the mail.
   * @param templateUrl - The path to the template file.
   * @param context - The context to render the template.
   */
  async sendSmtpWithTemplate(recipient: string, subject: string, templateUrl: string, context: object) {
    try {
      await this.mailer.sendMail({
        to: recipient,
        from: `"Green Pastures Team" <${this.from}>`,
        subject: subject,
        html: template(join(__dirname, templateUrl), context),
      });
      this.logger.log(`Mail sent to ${recipient}`);
    } catch (error) {
      this.logger.debug(`Resolved template path: ${join(__dirname, templateUrl)}`);
      throw new Error(error?.message);
    }
  }
}
