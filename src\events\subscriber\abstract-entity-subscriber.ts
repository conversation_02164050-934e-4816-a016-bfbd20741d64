import { EntitySubscriberInterface, EventSubscriber, InsertEvent, RemoveEvent, UpdateEvent } from "typeorm";
import { AbstractEntity } from '@common/entities/base.entity';

@EventSubscriber()
export class AbstractEntitySubscriber implements EntitySubscriberInterface<AbstractEntity> {
  /**
   * Listen to all entities that extend AbstractEntity.
   */
  listenTo() {
    return AbstractEntity;
  }

  /**
   * Before an entity is inserted.
   */
  beforeInsert(event: InsertEvent<AbstractEntity>): void {
    const currentUser = AbstractEntity.getCurrentUser();
    event.entity.createdBy = currentUser;
    event.entity.updatedBy = currentUser;
    event.entity.createdAt = new Date();
  }

  /**
   * Before an entity is updated.
   */
  beforeUpdate(event: UpdateEvent<AbstractEntity>): void {
    const currentUser = AbstractEntity.getCurrentUser();
    if (event.entity) {
      event.entity.updatedBy = currentUser;
      event.entity.updatedAt = new Date();
    }
  }

  /**
   * Before an entity is soft-deleted (optional).
   */
  beforeRemove(event: RemoveEvent<AbstractEntity>): void {
    const currentUser = AbstractEntity.getCurrentUser();
    if (event.entity) {
      event.entity.updatedBy = currentUser;
      event.entity.deletedAt = new Date();
    }
  }
}
