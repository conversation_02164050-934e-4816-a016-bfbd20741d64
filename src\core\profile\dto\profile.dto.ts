import { EntityDto } from '@common/dto/base.dto';
import { AutoMap } from "@automapper/classes";
import { Gender } from '@common/enumerations/gender.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { ApiProperty } from "@nestjs/swagger";
import { AddressDto } from '@core/address/dto/address.dto';


export class ProfileDto extends EntityDto {
  @AutoMap()
  @ApiProperty({
    type: "string",
    name: "firstName",
    description: "The first name of the user"
  })
  firstName: string;

  @AutoMap()
  @ApiProperty({
    type: "string",
    name: "lastName",
    description: "The last name of the user"
  })
  lastName: string;

  @AutoMap()
  @ApiProperty({
    type: "string",
    name: "email",
    description: "The email of the user"
  })
  email: string;

  @AutoMap()
  @ApiProperty({
    type: "string",
    name: "phoneNum<PERSON>",
    description: "The phone number of the user"
  })
  phoneNumber: string;

  @AutoMap(() => Boolean)
  @ApiProperty({
    type: "boolean",
    name: "verified",
    description: "Indicates if the user has been verified"
  })
  verified: boolean;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    enum: Object.values(Gender),
    name: "gender",
    description: "Possible gender types for users"
  })
  gender: Gender;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    enum: Object.values(ProfileStatus),
    name: "profileStatus",
    description: "Possible account status for users"
  })
  profileStatus: ProfileStatus;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    enum: Object.values(ProfileType),
    name: "profileType",
    description: "Possible account types for users"
  })
  profileType: ProfileType;

  @AutoMap(() => AddressDto)
  address: AddressDto;
}
