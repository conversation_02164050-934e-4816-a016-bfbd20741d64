import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ProfileService } from "./profile.service";
import { ProfileController } from "./profile.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { LoggerModule } from '@common/logger/logger.module';
import { Profile } from "./entities/profile.entity";
import { ProfileValidationService } from './profile.validation.service';
import { ProfileMapperService } from './profile.mapper.service';

@Module({
  controllers: [ProfileController],
  providers: [ProfileService, ProfileValidationService, ProfileMapperService],
  exports: [ProfileService, ProfileValidationService],
  imports: [TypeOrmModule.forFeature([Profile]), LoggerModule],
})
export class ProfileModule {}
