import { Injectable } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { JwtPayload } from "jsonwebtoken";

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor(
    private configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('keys.secret'),
      passReqToCallback: true,
      // algorithms: ['RS256'],
      // passReqToCallback: true,
    });
  }

  async validate(request: any, payload: JwtPayload) {
    const refreshToken = request
      .get('Authorization')
      .replace('Bearer', '')
      .trim();
    return { userId: payload.sub, email: payload.email, refreshToken };
  }
}
