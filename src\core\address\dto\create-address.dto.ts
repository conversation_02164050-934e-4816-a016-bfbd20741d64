import { AutoMap } from '@automapper/classes';
import { Column } from 'typeorm';

export class CreateAddressDto {
  @AutoMap()
  latitude: string;

  @AutoMap()
  longitude: string;

  @AutoMap()
  houseNumber: number;

  @AutoMap()
  street: string;

  @AutoMap()
  city: string;

  @AutoMap()
  @Column({ name: 'country', type: 'varchar' })
  country: string;

  @AutoMap()
  @Column({ name: 'state', type: 'varchar' })
  state: string;

  @AutoMap()
  @Column({ name: 'postal_code', type: 'varchar' })
  postalCode: string;
}
