import { config } from 'dotenv';

config();

export interface IRedisConfig {
  host: string;
  port: number;
  uri: string;
  db: number;
  password?: string;
  username?: string;
}

export const redisConfig = (): IRedisConfig => ({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT, 10),
  uri: process.env.REDIS_URI,
  db: parseInt(process.env.REDIS_DB, 10),
  password: process.env.REDIS_PASSWORD,
  username: process.env.REDIS_USERNAME,
});
