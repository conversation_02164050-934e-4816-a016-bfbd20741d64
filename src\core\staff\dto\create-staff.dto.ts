import { Gender } from '@common/enumerations/gender.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsEmail,
  IsNotEmpty,
  IsEnum,
  IsAlphanumeric,
} from 'class-validator';

export class CreateStaffDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  phoneNumber: string; // e.g., +2347012345678

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(Gender)
  gender: Gender;

  @ApiProperty()
  @IsNotEmpty()
  @IsAlphanumeric()
  password: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(ProfileType)
  profileType: ProfileType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  latitude?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  longitude?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  houseNumber: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  street: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  country: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  state: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  postalCode: string;
}
