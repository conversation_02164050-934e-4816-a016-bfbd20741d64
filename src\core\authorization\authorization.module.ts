import { Module } from '@nestjs/common';
import { RoleModule } from './role/role.module';
import { PermissionModule } from './permission/permission.module';
import { CaslAbilityFactoryService } from './casl-ability-factory/casl-ability-factory.service';
import { LoggerModule } from '../../common/logger/logger.module';
import { RolesGuard } from 'src/core/authorization/role/role.guard';
import { PermissionsGuard } from 'src/core/authorization/permission/permission.guard';
import { RolePermissionGuard } from 'src/core/authorization/role/rolePermissionguard';

@Module({
  imports: [RoleModule, PermissionModule, LoggerModule],
  providers: [CaslAbilityFactoryService, RolesGuard, PermissionsGuard, RolePermissionGuard],
  exports: [CaslAbilityFactoryService,RolesGuard, PermissionsGuard, RolePermissionGuard],
})
export class AuthorizationModule {}
