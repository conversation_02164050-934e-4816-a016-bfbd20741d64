import { NestFactory } from "@nestjs/core";
import { SeedersModule } from "./database/seeders/seeders.module";
import { LoggerService } from "./common/logger/logger.service";
import { SeederService } from "./database/seeders/seeder.service";

const boostrap = async () => {
  const appSeedContext = await NestFactory.createApplicationContext(SeedersModule);

  // Resolve LoggerService correctly for scoped provider
  const logger = await appSeedContext.resolve(LoggerService);
  logger.setContext('SeederBootstrap');

  try{
    const seeder = await appSeedContext.resolve(SeederService);
    logger.setContext('Seeder');
    logger.log('Starting seeding process');

    await seeder.seed();
    logger.log('Seeding completed successfully');
  }catch (error){
    logger.error('Seeding failed', error.stack ?? error);
    throw error?.stack ?? error;
  }finally {
    logger.log('Closing application context');
    await appSeedContext.close();
  }
}

(async () => {
  try{
    await boostrap();
  }catch (error){
    const logger = new LoggerService(); // Temporary instance if appContext couldn't be created
    logger.setContext('Bootstrap');
    logger.error('Failed to create application context', error.stack ?? error);
    process.exit(1); // Exit with failure code
  }
})();
