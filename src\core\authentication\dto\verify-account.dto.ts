import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';

export class VerifyAccountDto {
  @AutoMap(() => String)
  @ApiProperty({
    name: 'email',
    required: true,
    type: String,
  })
  @IsEmail()
  email: string;

  @AutoMap(() => String)
  @ApiProperty({
    name: 'otp',
    required: true,
    type: String,
  })
  @IsString()
  otp: string;
}
