import { Column, Entity } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';

@Entity({ name: 'address' })
export class Address extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'latitude', type: 'varchar' })
  latitude: string;

  @AutoMap()
  @Column({ name: 'longitude', type: 'varchar' })
  longitude: string;

  @AutoMap()
  @Column({ name: 'house_number', type: 'int' })
  houseNumber: number;

  @AutoMap()
  @Column({ name: 'street', type: 'text' })
  street: string;

  @AutoMap()
  @Column({ name: 'city', type: 'varchar' })
  city: string;

  @AutoMap()
  @Column({ name: 'country', type: 'varchar' })
  country: string;

  @AutoMap()
  @Column({ name: 'state', type: 'varchar' })
  state: string;

  @AutoMap()
  @Column({ name: 'postal_code', type: 'varchar' })
  postalCode: string;
}
