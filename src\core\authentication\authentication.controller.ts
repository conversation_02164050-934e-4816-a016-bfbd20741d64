import { Body, Controller, HttpCode, HttpStatus, Param, Post } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { I18nService } from 'nestjs-i18n';
import { AuthenticationService } from '@core/authentication/authentication.service';
import { IntegrationService } from '@core/integration/integration.service';
import { ProfileService } from '@core/profile/profile.service';
import { UserLoginDto } from '@core/authentication/dto/user-login.dto';
import { ForgotPasswordDto } from '@core/authentication/dto/forgot-password.dto';
import { CreateProfileDto } from '@core/profile/dto/create-profile.dto';
import { VerifyAccountDto } from './dto/verify-account.dto';
import { ResendVerificationOtpDto } from './dto/resend-verification-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileDto } from '@core/profile/dto/profile.dto';

@ApiTags('Authentication Endpoints')
@Controller({
  path: 'auth',
  version: '1',
})
export class AuthenticationController {
  constructor(
    private readonly logger: LoggerService,
    private readonly authService: AuthenticationService,
    private readonly integrationSvc: IntegrationService,
    private readonly profileService: ProfileService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(AuthenticationController.name);
  }

  @HttpCode(HttpStatus.CREATED)
  @Public()
  @ApiOperation({ summary: 'Account Sign Up' })
  @ApiBody({ type: CreateProfileDto })
  @Post('signup')
  async signup(@Body() createProfileDto: CreateProfileDto) {
    return CoreUtils.handleRequest(async () => {
      const profile = this.classMapper.map(createProfileDto, CreateProfileDto, Profile);
      await this.authService.signup(profile);
      return { message: this.i18n.t('message.success.sign_up'), data: null };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Account Login' })
  @ApiBody({ type: UserLoginDto })
  @Post('login')
  async login(@Body() loginDto: UserLoginDto) {
    return CoreUtils.handleRequest(async () => {
      const { emailOrPhoneNumber, password } = loginDto;
      const result = await this.authService.signin(emailOrPhoneNumber, password);
      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        profileInfo: this.classMapper.map(result.profile, Profile, ProfileDto),
      };
      return { message: this.i18n.t('message.success.login'), data };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Verify Account' })
  @HttpCode(HttpStatus.OK)
  @Post('verify-account')
  async verifyAccount(@Body() dto: VerifyAccountDto) {
    return CoreUtils.handleRequest(async () => {
      const { email, otp } = dto;
      await this.authService.verifyAccount(email, otp);

      return {
        message: this.i18n.t('message.success.account_verified'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Resend Verification OTP' })
  @HttpCode(HttpStatus.OK)
  @Post('resend-verification-otp')
  async resendOtp(@Body() dto: ResendVerificationOtpDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.resendVerificationOtp(dto.email);
      return {
        message: this.i18n.t('message.success.verification_otp_resent'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Forgot password' })
  @HttpCode(HttpStatus.OK)
  @Post('forgot-password')
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.forgotPassword(dto.email);
      return {
        message: this.i18n.t('message.success.password_reset_otp_sent'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Reset password' })
  @HttpCode(HttpStatus.OK)
  @Post('reset-password')
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.resetPassword(dto);
      return {
        message: this.i18n.t('message.success.reset_password'),
        data: null,
      };
    });
  }
}
