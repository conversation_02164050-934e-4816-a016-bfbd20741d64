import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class SetPasswordDto {
  @AutoMap(() => String)
  @ApiProperty({
    name: 'newPassword',
    description: 'New password',
    example: 'password',
    required: true,
    type: String,
  })
  newPassword: string;

  @AutoMap(() => String)
  @ApiProperty({
    name: 'confirmPassword',
    description: 'Confirm password',
    example: 'password',
    required: true,
    type: String,
  })
  confirmPassword: string;

  @AutoMap(() => Number)
  @ApiProperty({
    name: 'accountId',
    description: 'Account ID',
    example: 1,
    required: true,
    type: Number,
  })
  accountId: number;
}
