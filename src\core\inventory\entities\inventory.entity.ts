import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';
import { Store } from '@core/store/entities/store.entity';

/**
 * Inventory is the entity that represents the inventory of a product in a store.
 */
@Entity({ name: 'inventory' })
export class Inventory extends AbstractEntity {

  @AutoMap(() => Product)
  @OneToMany(() => Product, (product) => product.inventory)
  @JoinColumn({ name: 'product_id', referencedColumnName: 'id' })
  products: Product[]; // Product is the product associated with this inventory record.

  @AutoMap()
  @Column({ name: 'available_quantity', type: 'int' })
  availableQuantity: number; // Available quantity is the total quantity of the product that is available for sale.

  @AutoMap()
  @Column({ name: 'reserved_quantity', type: 'int' })
  reservedQuantity: number; // Reserved quantity is the total quantity of the product that is reserved for orders that have not yet been completed.

  @AutoMap(() => Store)
  @ManyToOne(() => Store, (store) => store.inventories)
  @JoinColumn({ name: 'store_id', referencedColumnName: 'id' })
  store: Store; // Store is the store associated with this inventory record.
}
