import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';
import { Address } from '@core/address/entities/address.entity';
import { Cart } from '@core/cart/entities/cart.entity';

@Entity({ name: 'cart_item' })
export class CartItem extends AbstractEntity{

  @AutoMap(() => Cart)
  @ManyToOne(() => Cart, (cart) => cart.items, { eager: false })
  @JoinColumn({ name: 'cart_id', referencedColumnName: 'id' })
  cart: Cart;

  @AutoMap(() => Product)
  @OneToOne(() => Address, { eager: true })
  @JoinColumn({ name: 'product_id', referencedColumnName: 'id' })
  product: Product;

  @AutoMap()
  @Column({name: 'quantity', type: 'int'})
  quantity: number;
}
