import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager, Repository } from 'typeorm';
import { Staff } from './entities/staff.entity';
import { EntityStatus } from '@common/entities/base.entity';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';

@Injectable()
export class StaffService implements EntityServiceStrategy<Staff> {
  constructor(
    @InjectRepository(Staff) private readonly staffRepository: Repository<Staff>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(StaffService.name);
  }

  async create(data: Staff): Promise<Staff> {
    return await this.entityManager.save(data);
  }

  modify(id: number, data: Staff): Promise<Staff> {
    throw new Error('Method not implemented.');
  }

  async findByPk(id: number): Promise<Staff> {
    return await this.staffRepository.findOneBy({ id });
  }

  async getOneStaff(id: number): Promise<Staff> {
    const staff = await this.findByPk(id);
    if (!staff) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: `Staff with id ${id}` } }));
    }
    return staff;
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const staff = await this.getOneStaff(id);
        staff.status = EntityStatus.ACTIVE;
        staff.profile.status = EntityStatus.ACTIVE;
        staff.profile.profileStatus = ProfileStatus.ACTIVE;
        await this.staffRepository.save(staff);
      }),
    );
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const staff = await this.getOneStaff(id);
        staff.status = EntityStatus.INACTIVE;
        staff.profile.status = EntityStatus.INACTIVE;
        staff.profile.profileStatus = ProfileStatus.INACTIVE;
        await this.staffRepository.save(staff);
      }),
    );
  }
}
