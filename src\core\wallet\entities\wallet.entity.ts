import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@common/entities/base.entity';
import { AutoMap } from '@automapper/classes';
import { Currency } from '@common/enumerations/currency.enum';

@Entity({ name: 'wallet' })
export class Wallet extends AbstractEntity {
  @AutoMap()
  @Column({
    name: 'currency',
    type: 'enum',
    enum: Currency,
  })
  currency: Currency;

  @AutoMap()
  @Column({ name: 'amount', type: 'decimal', precision: 10, scale: 2 })
  amount: number;
}
