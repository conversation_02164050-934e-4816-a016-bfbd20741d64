import { UserActions } from "../../../common/enumerations/user_actions.enum";
import { SeedPermissionDto } from "../../../core/authorization/permission/dto/seed-permission.dto";

export const permissions: Array<SeedPermissionDto> = [
  { name: UserActions.CREATE, description: 'Can Create Record' },
  { name: UserActions.DELETE, description: 'Can Delete Record' },
  { name: UserActions.READ, description: 'Can Read Record' },
  { name: UserActions.MANAGE, description: 'Can Manage Record' },
  { name: UserActions.UPDATE, description: 'Can Modify Record' },
];
