import { Injectable } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { Wallet } from '@core/wallet/entities/wallet.entity';

@Injectable()
export class WalletService implements EntityServiceStrategy<Wallet> {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext(WalletService.name);
  }

  activate(ids: number[]): Promise<void> {
    return Promise.resolve(undefined);
  }

  create(data: Wallet): Promise<Wallet> {
    return Promise.resolve(undefined);
  }

  deactivate(ids: number[]): Promise<void> {
    return Promise.resolve(undefined);
  }

  findByPk(id: number): Promise<Wallet | null> {
    return Promise.resolve(undefined);
  }

  modify(id: number, data: Wallet): Promise<Wallet> {
    return Promise.resolve(undefined);
  }

}
