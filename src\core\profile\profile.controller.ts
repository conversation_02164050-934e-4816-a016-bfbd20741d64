import { Controller } from "@nestjs/common";
import { ProfileService } from "./profile.service";
import { ApiTags } from "@nestjs/swagger";
import { LoggerService } from '@common/logger/logger.service';

@ApiTags("User Profile Endpoints")
@Controller({
  path: 'profile',
  version: '1',
})
export class ProfileController {
  constructor(private readonly profileService: ProfileService, private readonly logger: LoggerService) {
    this.logger.setContext(ProfileController.name);
  }
}
