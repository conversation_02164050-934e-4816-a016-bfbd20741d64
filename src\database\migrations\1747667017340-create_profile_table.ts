import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateProfileTable1747667017340 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'profile',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['A', 'I'],
            default: "'I'",
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },

          { name: 'first_name', type: 'varchar' },
          { name: 'last_name', type: 'varchar', isNullable: true },
          { name: 'email', type: 'varchar', isUnique: true },
          { name: 'phone_number', type: 'varchar', isUnique: true },
          { name: 'gender', type: 'enum', enum: ['MALE', 'FEMALE', 'NOT_SPECIFIED'], default: "'NOT_SPECIFIED'" },
          { name: 'profile_status', type: 'enum', enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'BANNED', 'DEACTIVATED'], default: "'INACTIVE'" },
          { name: 'profile_type', type: 'enum', enum: ['CLIENT', 'STAFF', 'NOT_SPECIFIED'], default: "'NOT_SPECIFIED'" },
          { name: 'verified', type: 'boolean', default: false },
          { name: 'password', type: 'text', isNullable: true },
          { name: 'password_reset_date', type: 'timestamp', isNullable: true },
          { name: 'failed_login_count', type: 'int', isNullable: true },
          { name: 'refresh_token', type: 'varchar', isNullable: true },
          { name: 'address_id', type: 'bigint', isNullable: true },
          { name: 'role_id', type: 'bigint', isNullable: true },
        ],
        foreignKeys: [
          {
            name: 'FK_PROFILE_ADDRESS',
            columnNames: ['address_id'],
            referencedTableName: 'address',
            referencedColumnNames: ['id'],
            onDelete: 'SET NULL',
          },
          {
            name: 'FK_PROFILE_ROLE',
            columnNames: ['role_id'],
            referencedTableName: 'role',
            referencedColumnNames: ['id'],
            onDelete: 'SET NULL',
          },
        ],
      }),
      true,
    );

    // Create indexes only for email and phone number
    await queryRunner.createIndex(
      'profile',
      new TableIndex({
        name: 'IDX_PROFILE_EMAIL',
        columnNames: ['email'],
        isUnique: true,
      }),
    );

    await queryRunner.createIndex(
      'profile',
      new TableIndex({
        name: 'IDX_PROFILE_PHONE',
        columnNames: ['phone_number'],
        isUnique: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('profile', 'IDX_PROFILE_EMAIL');
    await queryRunner.dropIndex('profile', 'IDX_PROFILE_PHONE');

    // Then drop the table (foreign keys will be dropped automatically)
    await queryRunner.dropTable('profile');
  }
}
