import { Injectable } from '@nestjs/common';
import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesGuard } from 'src/core/authorization/role/role.guard';
import { PermissionsGuard } from 'src/core/authorization/permission/permission.guard';

@Injectable()
export class RolePermissionGuard implements CanActivate {
  constructor(
    private readonly rolesGuard: RolesGuard,
    private readonly permissionsGuard: PermissionsGuard,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const rolesAllowed = await this.rolesGuard.canActivate(context); // Check roles
    const permissionsAllowed = await this.permissionsGuard.canActivate(context); // Check permissions
    
    return rolesAllowed && permissionsAllowed; // Return true if both pass
  }
}
