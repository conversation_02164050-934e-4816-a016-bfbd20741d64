import { AbstractEntity } from '@common/entities/base.entity';
import { Colum<PERSON>, En<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Address } from '@core/address/entities/address.entity';
import { Inventory } from '@core/inventory/entities/inventory.entity';

@Entity({ name: 'store' })
export class Store extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name' })
  name: string;

  @AutoMap(() => Address)
  @OneToOne(() => Address, { eager: true })
  @JoinColumn({ name: 'address_id', referencedColumnName: 'id' })
  address: Address;

  @AutoMap(() => Inventory)
  @OneToOne(() => Inventory, (inventory) => inventory.store)
  inventories: Inventory[]; // Inventory is the inventory associated with this store.
}
