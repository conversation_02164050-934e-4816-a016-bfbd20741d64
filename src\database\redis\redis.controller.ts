import {
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { RedisService } from './redis.service';
import { LoggerService } from '../../common/logger/logger.service';
import { CoreUtils } from '../../common/utils/core.utils';
import { Public } from "../../common/decorators/public.decorator";

@ApiTags('Cache Manager')
@Controller({
  path: 'cache',
  version: '1',
})
export class RedisController {
  constructor(
    private readonly redisService: RedisService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(RedisController.name);
  }

  @Public()
  @ApiOperation({ summary: 'Set cache key' })
  @Post()
  async setCacheKey(
    @Query('key') key: string,
    @Query('value') value: string,
    @Res() response: any,
  ) {
    try {
      await this.redisService.setCacheKey(key, value);
      return CoreUtils.genericResponse(
        response,
        HttpStatus.CREATED,
        'Key created',
      );
    } catch (error) {
      CoreUtils.throwError(error);
    }
  }

  @Public()
  @ApiOperation({ summary: 'Get cache key' })
  @Get('/get/:key')
  async getCacheKey(@Param('key') key: string, @Res() response: any) {
    try {
      if (!(await this.redisService.isKeyExists(key))) {
        this.logger.error(`Cache key not found: ${key}`);
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          'Cache key not found.',
        );
      } else {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.OK,
          'Cache key found.',
          await this.redisService.getCacheKey(key),
        );
      }
    } catch (error) {
      CoreUtils.throwError(error);
    }
  }

  @Public()
  @ApiOperation({ summary: 'Delete cache key' })
  @Delete('/:key')
  async deleteCacheKey(@Param('key') key: string, @Res() response: any) {
    try {
      if (!(await this.redisService.isKeyExists(key))) {
        this.logger.error(`Cache key not found: ${key}`);
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          'Cache key not found.',
        );
      }

      return CoreUtils.genericResponse(
        response,
        HttpStatus.OK,
        'Cache key deleted.',
        await this.redisService.delCacheKey(key),
      );
    } catch (error) {
      CoreUtils.throwError(error);
    }
  }
}
