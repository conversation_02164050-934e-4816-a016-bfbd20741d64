import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SeedPermissionDto } from '../../permission/dto/seed-permission.dto';

export class SeedRoleDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    name: 'name',
    description: 'The name of the role',
    required: true,
  })
  name: string;

  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    name: 'description',
    description: 'The description of the role',
  })
  description?: string;

  @AutoMap(() => SeedPermissionDto)
  @ApiProperty({ type: SeedPermissionDto, isArray: true })
  permissions?: SeedPermissionDto[];
}
