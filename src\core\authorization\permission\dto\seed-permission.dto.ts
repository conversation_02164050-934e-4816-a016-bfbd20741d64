import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SeedPermissionDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    name: 'name',
    description: 'The name of the permission',
    required: true,
  })
  name: string;

  @AutoMap(() => String)
  @IsString()
  @ApiProperty({
    name: 'description',
    description: 'The description of the permission',
  })
  description?: string;
}
