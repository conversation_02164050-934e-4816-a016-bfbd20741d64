import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { LoggerService } from '@common/logger/logger.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';

@Controller({
  path: 'customer',
  version: '1',
})
export class CustomerController {
  constructor(
    private readonly logger: LoggerService,
    private readonly customerService: CustomerService,
  ) {
    this.logger.setContext(CustomerController.name);
  }
}
