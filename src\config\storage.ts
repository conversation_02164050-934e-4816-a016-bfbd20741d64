import { config } from 'dotenv';

config();

export interface IStorageOptions {
  s3?: IAwsS3ConnectionOptions;
  gcs?: IGcpStorageOptions;
  cloudinary?: ICloudinaryStorageOption;
}

export interface IAwsS3ConnectionOptions {
  bucket: string;
  region: string;
  credentials: IAwsCredentials;
}

export interface IAwsCredentials {
  accessKey: string;
  secretKey: string;
}

export interface IGcpStorageOptions {
  bucket: string;
  credentialsJson: string;
}

export interface ICloudinaryStorageOption {
  cloudName: string;
  key: string;
  secret: string;
}

export const s3Config = (): IAwsS3ConnectionOptions => ({
  bucket: process.env.AWS_BUCKET,
  region: process.env.AWS_REGION,
  credentials: {
    accessKey: process.env.AWS_ACCESS_KEY,
    secretKey: process.env.AWS_SECRET_KEY,
  },
});

export const gcsConfig = (): IGcpStorageOptions => ({
  bucket: process.env.GCS_BUCKET,
  credentialsJson: process.env.GCS_CREDENTIALS,
});

export const cloudinaryConfig = (): ICloudinaryStorageOption => ({
  cloudName: process.env.CLOUDINARY_CLOUD_NAME,
  key: process.env.CLOUDINARY_API_KEY,
  secret: process.env.CLOUDINARY_API_SECRET,
});
