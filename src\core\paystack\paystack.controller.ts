import { Controller, Post, Query, Body, Get, Res, HttpStatus, BadRequestException } from '@nestjs/common';
import { initializePayment, verifyPayment, InitializePaymentArgs } from '../../api/paystackApi';
import { I18nService } from 'nestjs-i18n';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CoreUtils } from '@common/utils/core.utils';
import { Public } from 'src/common/decorators/public.decorator'

@ApiTags('Payment - Paystack')
@Controller({ path: 'paystack', version: '1' })
export class PaystackController {
  constructor(private readonly i18n: I18nService) {}

  @ApiOperation({ summary: 'Initialize Paystack Payment' })
  @Public()
  @Post('initialize')
  async init(@Res() response: any, @Body() body: any) {
    const { email, amount, name, callback_url } = body;

    return CoreUtils.handleRequest(async () => {
      const paymentDetails: InitializePaymentArgs = {
        email,
        amount: amount * 100, // convert to Kobo
        callback_url,
        metadata: { name, email, amount },
      };

      // Only include callback_url if provided
      if (callback_url) {
        paymentDetails.callback_url = callback_url;
      }

      const result = await initializePayment(paymentDetails);

      if (!result.status) {
        throw new BadRequestException(await this.i18n.t('errors.invalid_content'));
      }

      return CoreUtils.genericResponse(
        response,
        HttpStatus.OK,
        await this.i18n.t('success.created', { args: { entity: 'Payment' } }),
        result.data
      );
    });
  }

  @ApiOperation({ summary: 'Verify Paystack Payment' })
  @Public()
  @Get('verify')
  async verify(@Res() response: any, @Query('reference') reference: string) {
    if (!reference) {
      throw new BadRequestException(await this.i18n.t('errors.validation_error', { args: { entity: 'Reference' } }));
    }

    return CoreUtils.handleRequest(async () => {
      const result = await verifyPayment(reference);

      if (!result.status || result.data.status !== 'success') {
        throw new BadRequestException(await this.i18n.t('errors.invalid_content'));
      }

      return CoreUtils.genericResponse(
        response,
        HttpStatus.OK,
        await this.i18n.t('success.updated', { args: { entity: 'Payment' } }),
        result.data
      );
    });
  }
}
