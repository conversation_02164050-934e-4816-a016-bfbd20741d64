import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ValidationStrategy } from '../../../common/pattern/validation.strategy';
import { Role } from './entities/role.entity';
import { DatabaseAction } from '../../../common/enumerations/db_action.enum';
import { LoggerService } from '../../../common/logger/logger.service';
import { Repository } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class RoleValidationService implements ValidationStrategy<Role> {
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
  ) {
    this.logger.setContext(RoleValidationService.name);
  }

  async validate(data: Role, action: DatabaseAction) {
    const existingRole: Role = await this.roleRepo.findOne({
      where: { name: data.name },
    });

    if (DatabaseAction.CREATE === action && existingRole) {
      throw new InternalServerErrorException(
        this.i18n.t('role.errors.role_already_exists', {
          args: { name: data.name },
        }),
      );
    }

    if (DatabaseAction.UPDATE === action && !existingRole) {
      throw new NotFoundException(this.i18n.t('role.errors.role_not_found'));
    }
  }
}
