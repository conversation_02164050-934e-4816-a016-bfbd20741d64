import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';

export class ResetPasswordDto {
  @AutoMap(() => String)
  @ApiProperty({
    name: 'email',
    required: true,
    type: String,
  })
  @IsEmail()
  email: string;

  @AutoMap(() => String)
  @ApiProperty({
    name: 'otp',
    required: true,
    type: String,
  })
  @IsString()
  otp: string;

  @AutoMap(() => String)
  @ApiProperty({
    name: 'newPassword',
    description: 'New password',
    example: 'password',
    required: true,
    type: String,
  })
  newPassword: string;
}
