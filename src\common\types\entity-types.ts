export type AuthDetails = {
  password?: string;
  passwordResetDate?: Date;
  failedLoginCount?: number;
}

export type SettingsDetails = {
  [key: string]: any;
}

export type OtpDetails = {
  otp?: string;
  expiryDate?: Date;
}

export type ProfileVerificationDetails = {
  kycVerified?: boolean;
  verified?: boolean;
}

export type PictureDetails = {
  url?: string;
  publicId?: string;
}

export type WalletDetails = {
  balance: number;
  currency: string;
  paystackCustomerId: string;
}
