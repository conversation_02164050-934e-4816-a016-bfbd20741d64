import { Options, TransportType } from 'nestjs-mailer';
import { config } from 'dotenv';
import { join } from 'path';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';

config();

export interface IMailerConfig {
  transport?: TransportType;
  defaults?: Options;
  sendGridApiKey?: string;
  template?: any;
}

export const mailerConfig = (): IMailerConfig => ({
  transport: {
    service: process.env.MAIL_SERVICE,
    host: process.env.MAIL_HOST,
    port: parseInt(process.env.MAIL_PORT, 10) || 587,
    secure: false,
    auth: {
      user: process.env.MAIL_USERNAME,
      pass: process.env.MAIL_PASSWORD,
    },
  },
  sendGridApiKey: process.env.SENDGRID_API_KEY,
  defaults: {
    from: process.env.MAIL_FROM,
  },
  template: {
    dir: join(__dirname, '../core/notification/mail/template'),
    adapter: new HandlebarsAdapter(),
    option: { strict: true },
  },
});
