import { Modu<PERSON> } from "@nestjs/common";
import { PermissionSeederService } from "./permission-seeder.service";
import { LoggerModule } from "../../../common/logger/logger.module";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Permission } from "../../../core/authorization/permission/entities/permission.entity";

@Module({
  providers: [PermissionSeederService],
  exports: [PermissionSeederService],
  imports: [
    LoggerModule,
    TypeOrmModule.forFeature([Permission]),
  ],
})
export class PermissionSeederModule {}
