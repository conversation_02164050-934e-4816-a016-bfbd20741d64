import { AbstractEntity } from '@common/entities/base.entity';
import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class ProfileContextInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHand<PERSON>): Observable<any> {
    const request = context.switchToHttp().getRequest();
    console.log('ProfileContextInterceptor -> request', request);
    const profile = request.user;

    let userName = 'SYSTEM';
    if (profile?.firstName && profile?.lastName) {
      userName = `${profile.firstName} ${profile.lastName}`;
    }

    AbstractEntity.setCurrentUser(userName || 'ANONYMOUS');
    return next.handle();
  }
}
