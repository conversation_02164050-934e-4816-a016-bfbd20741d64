import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, Join<PERSON><PERSON>um<PERSON>, OneToOne, Unique } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { Gender } from '@common/enumerations/gender.enum';
import { Role } from '@core/authorization/role/entities/role.entity';
import { Address } from '@core/address/entities/address.entity';

@Entity({ name: 'profile' })
@Unique(['email', 'phoneNumber'])
export class Profile extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'first_name', type: 'varchar' })
  firstName: string;

  @AutoMap()
  @Column({ name: 'last_name', type: 'varchar', nullable: true })
  lastName: string;

  @AutoMap()
  @Column({ name: 'email', type: 'varchar', unique: true })
  email: string;

  @AutoMap()
  @Column({ name: 'phone_number', type: 'varchar', unique: true })
  phoneNumber: string; // Phone number format should start with country code, e.g., +2347012345678

  @AutoMap(() => String)
  @Column({
    name: 'gender',
    type: 'varchar',
    enum: Gender,
    default: Gender.DEFAULT,
  })
  gender: Gender;

  @AutoMap(() => String)
  @Column({
    name: 'profile_status',
    type: 'enum',
    enum: ProfileStatus,
    default: ProfileStatus.DEACTIVATED,
  })
  profileStatus: ProfileStatus; // Set profile status to INACTIVE if the status of is INACTIVE.

  @AutoMap(() => String)
  @Column({
    name: 'profile_type',
    type: 'enum',
    enum: ProfileType,
    default: ProfileType.NOT_SPECIFIED,
  })
  profileType: ProfileType;

  @AutoMap()
  @Column({ name: 'verified', type: 'boolean', default: false })
  verified?: boolean; // Verified is true if OTP is confirmed to be valid. If verified is true, then the profile and entity status should be set to ACTIVE.

  @AutoMap()
  @Column({ name: 'password', type: 'text', nullable: true })
  password: string;

  @AutoMap()
  @Column({ name: 'password_reset_date', type: 'timestamp', nullable: true })
  passwordResetDate?: Date;

  @AutoMap()
  @Column({ name: 'failed_login_count', type: 'int', nullable: true })
  failedLoginCount: number; // Number of failed login attempts. If this is greater than 3, then the profile should be set to SUSPENDED.

  @AutoMap()
  @Column({
    name: 'refresh_token',
    nullable: true,
    type: 'text',
    default: null,
  })
  refreshToken: string;

  @AutoMap(() => Address)
  @OneToOne(() => Address, { cascade: true, eager: true })
  @JoinColumn({ name: 'address_id', referencedColumnName: 'id' })
  address: Address;

  @AutoMap(() => Role)
  @OneToOne(() => Role, { cascade: true, eager: true })
  @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })
  role?: Role; // If profileType is CUSTOMER, then the role should be defaulted to CUSTOMER_ROLE.
}
