import axios from 'axios';
import { paystackConfig } from '../config/paystack.config';

const { baseUrl, secretKey } = paystackConfig();

const headers = {
  Authorization: `Bearer ${secretKey}`,
  'Content-Type': 'application/json',
};

export interface InitializePaymentArgs {
  email: string;
  amount: number;
  callback_url?: string;
  metadata?: {
    name: string;
    email: string;
    amount: number;
  };
}

export const initializePayment = async (data: InitializePaymentArgs) => {
  const res = await axios.post(
    `${baseUrl}/transaction/initialize`,
    data,
    { headers }
  );
  return res.data;
};

export const verifyPayment = async (reference: string) => {
  const res = await axios.get(
    `${baseUrl}/transaction/verify/${reference}`,
    { headers }
  );
  return res.data;
};
