import { Module } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { WalletController } from './wallet.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Wallet } from '@core/wallet/entities/wallet.entity';
import { LoggerModule } from '@common/logger/logger.module';

@Module({
  controllers: [WalletController],
  providers: [WalletService],
  exports: [WalletService],
  imports: [
    LoggerModule,
    TypeOrmModule.forFeature([Wallet]), // Add your entities here
  ],
})
export class WalletModule {}
