import { Role } from 'src/core/authorization/role/roles.enum';
import { Permission } from '../../../authorization/permission/permission.enum';

export const RolePermissions: Record<Role, Permission[]> = {
  [Role.USER]: [Permission.CREATE_TASK],
  [Role.MANAGER]: [Permission.CREATE_TASK, Permission.UPDATE_TASK],
  [Role.ADMIN]: [
    Permission.CREATE_TASK,
    Permission.UPDATE_TASK,
    Permission.DELETE_TASK,
    Permission.VIEW_USERS,
  ],
};
