import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';

export class AuthInfoDto {
  @AutoMap()
  @ApiProperty({
    name: 'password',
    type: String,
    required: false,
    description: 'The password of the account'
  })
  password: string;

  @AutoMap()
  @ApiProperty({
    name:'lastPasswordResetDate',
    type: Date,
    required: false,
    description: 'The last password reset date'
  })
  passwordResetDate: Date;

  @AutoMap()
  @ApiProperty({
    name: 'failedLoginCount',
    type: Number,
    required: false,
    description: 'The number of failed login attempts'
  })
  failedLoginCount: number;
}
