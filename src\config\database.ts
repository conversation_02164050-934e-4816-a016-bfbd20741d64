import { config } from 'dotenv';
import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { AbstractEntitySubscriber } from '@events/subscriber/abstract-entity-subscriber';
import migrations from '@database/migrations';

config({ path: '.env' });

export const dbConfig = (): PostgresConnectionOptions => ({
  type: 'postgres',
  host: process.env.PG_HOST,
  port: parseInt(process.env.PG_PORT, 10) || 5432,
  url: `postgres://${process.env.PG_USERNAME}:${process.env.PG_PASSWORD}@${process.env.PG_HOST}:${process.env.PG_PORT}/${process.env.PG_DATABASE}`,
  username: process.env.PG_USERNAME,
  password: process.env.PG_PASSWORD,
  database: process.env.PG_DATABASE,
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  subscribers: [AbstractEntitySubscriber],
  synchronize: false,
  dropSchema: false,
  migrationsRun: false,
  logging: true,
  migrations,
});

if (process.env.NODE_ENV === 'development') {
  Logger.debug(dbConfig());
}

export const AppDataSource = new DataSource(dbConfig());
