import { PaginationDto } from "../dto/pagination.dto";
import { PaginatedResponseDto } from "../dto/paginated-response.dto";

export interface EntityServiceStrategy<T> {
  create(data: T): Promise<T>;
  modify(id: number, data: T): Promise<T>;
  findByPk(id: number): Promise<T | null>;
  activate(ids: number[]): Promise<void>;
  deactivate(ids: number[]): Promise<void>;
  table?<R = T>(
    paginationDto: PaginationDto,
    ...args: Array<string | number>
  ): Promise<PaginatedResponseDto<R>>;
}
