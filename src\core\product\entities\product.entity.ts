import { AbstractEntity } from '@common/entities/base.entity';
import { AutoMap } from '@automapper/classes';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne, Unique } from 'typeorm';
import { Category } from '@core/category/entities/category.entity';
import { Inventory } from '@core/inventory/entities/inventory.entity';

@Entity({ name: 'product' })
@Unique(['name'])
export class Product extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', unique: true })
  name: string;

  @AutoMap()
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @AutoMap(() => Category)
  @ManyToOne(() => Category, (category) => category.products, { eager: true })
  @JoinColumn({ name: 'category_id', referencedColumnName: 'id' })
  category: Category;

  @AutoMap()
  @Column({ name: 'price', type: 'decimal', precision: 10, scale: 2, default: 0 })
  price: number;

  @AutoMap(() => Inventory)
  @ManyToOne(() => Inventory, (inventory) => inventory.products)
  @JoinColumn({ name: 'inventory_id', referencedColumnName: 'id' })
  inventory: Inventory;
}
