import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateProfileTable1739614413538 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'account',
        columns: [
          { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
          { name: 'status', type: 'varchar' },
          { name: 'created_at', type: 'timestamp', default: 'now()' },
          { name: 'created_by', type: 'varchar' },
          { name: 'updated_at', type: 'timestamp', default: 'now()' },
          { name: 'updated_by', type: 'varchar', isNullable: true },
          { name: 'deleted_at', type: 'timestamp', isNullable: true },
          { name: 'first_name', type: 'varchar' },
          { name: 'last_name', type: 'varchar', isNullable: true },
          { name: 'email', type: 'varchar', isUnique: true },
          { name: 'phone_number', type: 'varchar', isUnique: true },
          { name: 'gender', type: 'varchar' },
          { name: 'profile_status', type: 'varchar' },
          { name: 'profile_type', type: 'varchar' },
          { name: 'verified', type: 'boolean', default: false },
          { name: 'password', type: 'text', isNullable: true },
          { name: 'password_reset_date', type: 'timestamp', isNullable: true },
          { name: 'failed_login_count', type: 'int', isNullable: true },
          { name: 'otp', type: 'varchar', isNullable: true },
          { name: 'otp_expiry_date', type: 'timestamp', isNullable: true },
          { name: 'address_id', type: 'bigint', isNullable: true },
          { name: 'role_id', type: 'bigint', isNullable: true },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'account',
      new TableIndex({
        name: 'IDX_ACCOUNT_FIELDS',
        columnNames: [
          'id',
          'status',
          'created_at',
          'updated_at',
          'first_name',
          'last_name',
          'email',
          'phone_number',
          'gender',
          'profile_status',
          'profile_type',
          'role_id',
          'address_id',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('account', 'IDX_ACCOUNT_FIELDS');
    await queryRunner.dropTable('account');
  }
}
