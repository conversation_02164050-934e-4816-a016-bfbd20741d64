import { Module } from '@nestjs/common';
import { LoggerModule } from "../../common/logger/logger.module";
import { MailService } from "./mail/mail.service";
import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';

@Module({
  controllers: [NotificationController],
  providers: [NotificationService, MailService],
  exports: [NotificationService, MailService],
  imports: [LoggerModule],
})
export class NotificationModule {}
