import { Test, TestingModule } from '@nestjs/testing';
import { RoleValidationService } from './role.validation.service';

describe('RoleValidatorService', () => {
  let service: RoleValidationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RoleValidationService],
    }).compile();

    service = module.get<RoleValidationService>(RoleValidationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
