import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';

/*
    Follow this tutorial to get required credentials:
    https://dev.to/imichaelowolabi/how-to-implement-login-with-google-in-nest-js-2aoa

	Follow this tutorial for learning more:
	https://blog.logrocket.com/implement-secure-single-sign-on-nestjs-google/
*/

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      clientID: configService.get<string>('oauth.google.clientId'),
      clientSecret: configService.get<string>('oauth.google.secret'),
      callbackURL: `http://localhost:${configService.get<string>(
        'port',
      )}/auth/google/callback`,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    done(null, profile);
  }
}
