import { OtpType } from '@common/enumerations/otp_type.enum';
import { CoreConstants } from '@common/utils/core.constants';
import { CoreUtils } from '@common/utils/core.utils';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { Cache } from 'cache-manager';

@Injectable()
export class OtpService {
  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache) {}

  async createOtp(userId: number, type: OtpType) {
    const otp = CoreUtils.generateOtp(CoreConstants.OTP_LENGTH);
    const key = CoreUtils.getOtpCacheKey(userId, type);
    const expiresAt = Date.now() + CoreConstants.OTP_TTL * 60 * 1000;
    const ttl = 60 * 60 * 1000;
    const cacheValue = {
      otp,
      expiresAt,
    };
    await this.cacheManager.set(key, cacheValue, ttl);
    return otp;
  }

  async verifyOtp(userId: number, type: OtpType, otp: string) {
    const key = CoreUtils.getOtpCacheKey(userId, type);
    const cached = await this.cacheManager.get<{ otp: string; expiresAt: number }>(key);

    if (!cached) {
      throw new NotFoundException('OTP not found');
    }

    if (cached.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    if (Date.now() > cached.expiresAt) {
      throw new BadRequestException('OTP has expired');
    }

    await this.cacheManager.del(key);
  }

}
