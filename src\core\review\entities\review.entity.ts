import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';
import { Address } from '@core/address/entities/address.entity';
import { Customer } from '@core/customer/entities/customer.entity';

@Entity({ name: 'review' })
export class Review extends AbstractEntity {
  @AutoMap()
  @Column({name: 'rating', type: 'int', default: 0})
  rating: number;

  @AutoMap()
  @Column({name: 'comment', type: 'text', nullable: true})
  comment: string;

  @AutoMap(() => Product)
  @OneToOne(() => Address, { eager: true })
  @JoinColumn({ name: 'product_id', referencedColumnName: 'id' })
  product: Product;

  @AutoMap(() => Customer)
  @ManyToOne(() => Customer, (customer) => customer.reviews)
  @JoinColumn({ name: 'customer_id', referencedColumnName: 'id' })
  customer: Customer;
}
