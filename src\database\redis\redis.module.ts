import { Module } from '@nestjs/common';
import { CacheInterceptor, CacheModule } from '@nestjs/cache-manager';
import { RedisService } from './redis.service';
import { RedisController } from './redis.controller';
import { LoggerModule } from '../../common/logger/logger.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import KeyvRedis, { Keyv } from '@keyv/redis';
import { CacheableMemory } from 'cacheable';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    LoggerModule,
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: async (configService: ConfigService) => {
        return {
          ttl: 60 * 1000,
          refreshThreshold: 1200,
          stores: [
            new Keyv({
              store: new CacheableMemory({ ttl: 60000, lruSize: 5000 }),
            }),
            new KeyvRedis(
              configService.get<string>('REDIS_URI')
            ),
          ],
        };
      },
    }),
  ],
  controllers: [RedisController],
  providers: [
    RedisService,
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: CacheInterceptor,
    // },
  ],
  exports: [RedisService],
})
export class RedisModule {}
