export interface StorageStrategy {
  uploadFile(
    file?: Buffer,
    resourceType?: 'image' | 'video' | 'auto',
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<any>;

  fileExists?(
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<boolean>;

  deleteFile(hash?: string, folder?: string, extension?: string): Promise<any>;

  getSignedUrl?(
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<string>;

  getObject?(hash?: string, folder?: string, extension?: string): Promise<any>;

  uploadPublicFile?(
    file?: Buffer,
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<any>;

  getPublicUrl?(
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<string>;

  copyFileToFolder?(
    sourceFolder: string,
    sourceHash: string,
    extension: string,
    destinationFolder: string,
    destinationHash: string,
  ): Promise<void>;
}
