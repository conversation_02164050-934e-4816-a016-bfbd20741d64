import { ApiProperty } from "@nestjs/swagger";
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { AutoMap } from "@automapper/classes";
import { SeedRoleDto } from "../../authorization/role/dto/seed-role.dto";
import { IsString } from 'class-validator';

export class CreateStaffProfileDto {
  @ApiProperty({
    name:'firstName',
    type: String,
    required: true,
    description: 'The first name of the user'
  })
  @IsString({
    message: 'First name must be a string',
  })
  firstName: string;

  @ApiProperty({
    name:'lastName',
    type: String,
    required: true,
    description: 'The last name of the user'
  })
  @IsString({
    message: 'Last name must be a string',
  })
  lastName: string;

  @ApiProperty({
    name:'email',
    type: String,
    required: true,
    description: 'The email of the user'
  })
  email: string;

  @ApiProperty({
    name:'phoneNumber',
    type: String,
    required: true,
    description: 'The phone number of the user',
    example: '+2348123456789'
  })
  phoneNumber: string;

  @ApiProperty({
    name:'password',
    type: String,
    required: true,
    description: 'The password of the user'
  })
  @IsString({
    message: 'Password must be a string',
  })
  password: string;

  @ApiProperty({
    name:'profileType',
    type: String,
    enum: ProfileType,
    description: 'The profile type of the user'
  })
  @AutoMap(() => String)
  @IsString({
    message: 'Profile type must be a string',
  })
  profileType: ProfileType

  @AutoMap(() => SeedRoleDto)
  @ApiProperty({
    name: 'role',
    type: Number,
    description: 'The role of the profile',
  })
  role?: SeedRoleDto;
}
