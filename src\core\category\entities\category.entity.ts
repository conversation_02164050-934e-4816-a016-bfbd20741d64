import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, OneToMany, Unique } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';

@Entity({name: 'category'})
@Unique(['name'])
export class Category extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name' })
  name: string;

  @AutoMap()
  @Column({ name: 'description' })
  description: string;

  @AutoMap(() => Product)
  @OneToMany(() => Product, (product) => product.category)
  products: Product; // Product is the product associated with this category.
}
