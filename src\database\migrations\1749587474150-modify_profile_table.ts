import { MigrationInterface, QueryRunner, TableColumn, TableIndex } from 'typeorm';

export class ModifyProfileTable1749587474150 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('profile', 'status');
    await queryRunner.addColumn(
      'profile',
      new TableColumn({
        name: 'status',
        type: 'varchar',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('profile', 'status');
    await queryRunner.addColumn(
      'profile',
      new TableColumn({
        name: 'status',
        type: 'enum',
        enum: ['A', 'I'],
        default: "'I'",
      }),
    );
  }
}
