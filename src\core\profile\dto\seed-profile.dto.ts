import { ApiProperty } from "@nestjs/swagger";
import { ProfileType } from "../../../common/enumerations/profile_type.enum";
import { AutoMap } from "@automapper/classes";
import { SeedRoleDto } from "../../authorization/role/dto/seed-role.dto";

export class SeedProfileDto {
  @ApiProperty({
    name:'firstName',
    type: String,
    required: true,
    description: 'The first name of the user'
  })
  firstName: string;

  @ApiProperty({
    name:'lastName',
    type: String,
    required: true,
    description: 'The last name of the user'
  })
  lastName: string;

  @ApiProperty({
    name:'email',
    type: String,
    required: true,
    description: 'The email of the user'
  })
  email: string;

  @ApiProperty({
    name:'phoneNumber',
    type: String,
    required: true,
    description: 'The phone number of the user',
    example: '+*************'
  })
  phoneNumber: string;

  @ApiProperty({
    name:'password',
    type: String,
    required: true,
    description: 'The password of the user'
  })
  password: string;

  @ApiProperty({
    name:'accountType',
    type: String,
    enum: ProfileType,
    description: 'The account type of the user'
  })
  accountType: ProfileType

  @AutoMap(() => SeedRoleDto)
  @ApiProperty({
    name: 'role',
    type: Number,
    description: 'The role of the account',
  })
  role: SeedRoleDto;
}
