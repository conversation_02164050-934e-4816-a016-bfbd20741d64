import { <PERSON>du<PERSON> } from "@nestjs/common";
import { LoggerModule } from '@common/logger/logger.module';
import { NotificationModule } from '@core/notification/notification.module';
import { NotificationListenerService } from "./listener/notification.listener.service";


@Module({
  providers: [NotificationListenerService],
  exports: [NotificationListenerService],
  imports: [LoggerModule, NotificationModule],
})
export class EventsModule {}
