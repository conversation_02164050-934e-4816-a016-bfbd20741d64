import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class UserLoginDto {
  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The email or phone number of the user',
    name: 'emailOrPhoneNumber',
  })
  @IsString()
  @IsNotEmpty()
  emailOrPhoneNumber: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The password of the user',
    name: 'password',
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
