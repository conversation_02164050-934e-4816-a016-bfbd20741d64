import { ApiProperty } from '@nestjs/swagger';
import { Currency } from '@common/enumerations/currency.enum';
import { IsEnum, IsNumber } from 'class-validator';

export class CreateWalletDto {
  @ApiProperty({
    description: 'Currency of the wallet',
    enum: Currency,
    name: 'currency',
    example: Currency.NGN,
  })
  @IsEnum(Currency, {
    message: 'Currency must be a valid enum value',
  })
  currency: Currency;

  @ApiProperty({
    description: 'Amount in the wallet',
    type: Number,
    name: 'amount',
    example: 1000,
  })
  @IsNumber({}, {
    message: 'Amount must be a number',
  })
  amount: number;
}
