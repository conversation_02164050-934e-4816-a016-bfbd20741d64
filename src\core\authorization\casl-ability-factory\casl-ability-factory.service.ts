import { Injectable } from '@nestjs/common';
import { AbilityBuilder, AbilityClass, ExtractSubjectType, InferSubjects, PureAbility } from "@casl/ability";
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { Role } from '@core/authorization/role/entities/role.entity';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';
import { ProfileType } from '@common/enumerations/profile_type.enum';


export type Subjects = InferSubjects<typeof Profile | typeof Role> | 'all';

export type AppAbility = PureAbility<[UserActions, Subjects]>;

@Injectable()
export class CaslAbilityFactoryService {
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(CaslAbilityFactoryService.name);
  }

  createForUser(profile: Profile) {
    const { can, cannot, build } = new AbilityBuilder<AppAbility>(
      PureAbility as AbilityClass<AppAbility>,
    );

    if(profile.profileType === ProfileType.STAFF){
      profile.role.permissions.forEach((permission) => {
        if (permission.name === UserActions.MANAGE) {
          can(UserActions.MANAGE, 'all'); // Can manage all subjects
        } else {
          can(permission.name as UserActions, Profile); // Adjust this for specific permissions
        }
      });

      // Example: Prevent 'delete' on 'User'
      cannot(UserActions.DELETE, Profile).because(
        this.i18n.t('ability.msg', { args: { entity: 'profiles' } }),
      );
      cannot(UserActions.DELETE, Role).because(
        this.i18n.t('ability.msg', { args: { entity: 'roles' } }),
      );
    }

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
