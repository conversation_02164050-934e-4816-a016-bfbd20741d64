import { Modu<PERSON> } from "@nestjs/common";
import { AccountSeederService } from "./account-seeder.service";
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from "@nestjs/typeorm";
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { Role } from '@core/authorization/role/entities/role.entity';
import { Profile } from '@core/profile/entities/profile.entity';


@Module({
  providers: [AccountSeederService],
  exports: [AccountSeederService],
  imports: [
    LoggerModule,
    TypeOrmModule.forFeature([Profile, Role, Permission]),
  ]
})
export class AccountSeederModule {}
