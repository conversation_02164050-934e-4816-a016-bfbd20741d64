import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { MulterModule } from '@nestjs/platform-express';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { MailerModule } from 'nestjs-mailer';
import { join } from 'path';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import configuration from './config';

import { CommonModule } from './common/common.module';
import { LoggerModule } from './common/logger/logger.module';
import { AuthorizationModule } from './core/authorization/authorization.module';
import { CoreModule } from './core/core.module';
import { MailModule } from './core/notification/mail/mail.module';
import { EventsModule } from './events/events.module';

import { RolePermissionGuard } from './core/authorization/role/rolePermissionguard';
import { ProfileContextInterceptor } from './profile-context/profile-context.interceptor';

@Module({
  imports: [
    LoggerModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<object>('database'),
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        config: configService.get('mails'),
      }),
      inject: [ConfigService],
    }),
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
    CacheModule.register({ isGlobal: true }),
    MailModule,
    AuthorizationModule,
    CommonModule,
    CoreModule,
    EventsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ProfileContextInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: RolePermissionGuard,
    },
  ],
})
export class AppModule {}
