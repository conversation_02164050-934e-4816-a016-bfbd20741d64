import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { LoggerService } from '../../../common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { Permission } from './entities/permission.entity';
import { Repository } from 'typeorm';
import { ValidationStrategy } from '../../../common/pattern/validation.strategy';
import { DatabaseAction } from '../../../common/enumerations/db_action.enum';

@Injectable()
export class PermissionValidationService
  implements ValidationStrategy<Permission>
{
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Permission)
    private readonly permissionRepo: Repository<Permission>,
  ) {
    this.logger.setContext(PermissionValidationService.name);
  }

  async validate(data: Permission, action: DatabaseAction) {
    const existingPermission = await this.permissionRepo.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE === action && existingPermission) {
      throw new InternalServerErrorException(
        this.i18n.t('permission.errors.permission_already_exists', {
          args: { name: data.name },
        }),
      );
    }

    if (DatabaseAction.UPDATE === action && !existingPermission) {
      throw new NotFoundException(
        this.i18n.t('permission.errors.permission_not_found'),
      );
    }
  }
}
