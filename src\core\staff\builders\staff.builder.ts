import { Gender } from '@common/enumerations/gender.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { Address } from '@core/address/entities/address.entity';
import { Staff } from '../entities/staff.entity';
import { Profile } from '@core/profile/entities/profile.entity';

export class StaffBuilder {
  private firstName: string;
  private lastName: string;
  private email: string;
  private phoneNumber: string; // Phone number format should start with country code, e.g., +2347012345678
  private gender: Gender;
  private password: string;
  private profileType: ProfileType;
  private latitude?: string;
  private longitude?: string;
  private houseNumber: number;
  private street: string;
  private city: string;
  private country: string;
  private state: string;
  private postalCode: string;
  constructor(firstName: string) {
    this.firstName = firstName;
  }

  withLastName(lastName: string): this {
    this.lastName = lastName;
    return this;
  }

  withEmail(email: string): this {
    this.email = email;
    return this;
  }

  withPhoneNumber(phoneNumber: string): this {
    this.phoneNumber = phoneNumber;
    return this;
  }

  withPassword(password: string): this {
    this.password = password;
    return this;
  }

  withGender(gender: Gender): this {
    this.gender = gender;
    return this;
  }

  withprofileType(profileType: ProfileType): this {
    this.profileType = profileType;
    return this;
  }

  withLatitude(latitude: string): this {
    this.latitude = latitude;
    return this;
  }
  withLongitude(longitude: string): this {
    this.longitude = longitude;
    return this;
  }
  withHouseNumber(houseNumber: number): this {
    this.houseNumber = houseNumber;
    return this;
  }
  withStreet(street: string): this {
    this.street = street;
    return this;
  }
  withCity(city: string): this {
    this.city = city;
    return this;
  }
  withCountry(country: string): this {
    this.country = country;
    return this;
  }

  withState(state: string): this {
    this.state = state;
    return this;
  }

  withPostalCode(postalCode: string): this {
    this.postalCode = postalCode;
    return this;
  }

  build(): Staff {
    const profile = new Profile();
    const address = new Address();
    console.log('profile ', profile);
    console.log('address ', address);
    const staff = new Staff();
    staff.profile = profile;
    staff.profile.address = address;

    console.log('profile1 ', staff.profile);
    console.log('address1 ', staff.profile.address);
    staff.profile.firstName = this.firstName;
    staff.profile.lastName = this.lastName;
    staff.profile.email = this.email;
    staff.profile.phoneNumber = this.phoneNumber;
    staff.profile.password = this.password;
    staff.profile.gender = this.gender;
    staff.profile.profileType = this.profileType;
    staff.profile.address.latitude = this.latitude;
    staff.profile.address.longitude = this.longitude;
    staff.profile.address.houseNumber = this.houseNumber;
    staff.profile.address.street = this.street;
    staff.profile.address.city = this.city;
    staff.profile.address.country = this.country;
    staff.profile.address.state = this.state;
    staff.profile.address.postalCode = this.postalCode;
    return staff;
  }
}
//
