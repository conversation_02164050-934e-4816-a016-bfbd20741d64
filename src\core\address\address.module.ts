import { Modu<PERSON> } from '@nestjs/common';
import { AddressService } from './address.service';
import { AddressController } from './address.controller';
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Address } from '@core/address/entities/address.entity';

@Module({
  controllers: [AddressController],
  exports: [AddressService],
  imports: [
    LoggerModule,
    TypeOrmModule.forFeature([Address]), // Add your entities here
  ],
  providers: [AddressService],
})
export class AddressModule {}
