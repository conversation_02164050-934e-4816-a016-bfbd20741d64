import { Module } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CustomerController } from './customer.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Customer } from '@core/customer/entities/customer.entity';
import { LoggerModule } from '@common/logger/logger.module';

@Module({
  controllers: [CustomerController],
  providers: [CustomerService],
  exports: [CustomerService],
  imports: [
    TypeOrmModule.forFeature([Customer]),
    LoggerModule
  ],
})
export class CustomerModule {}
