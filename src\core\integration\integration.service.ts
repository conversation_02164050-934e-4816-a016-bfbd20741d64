import { Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { ConfigService } from "@nestjs/config";
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { CoreUtils } from '@common/utils/core.utils';

@Injectable()
export class IntegrationService {
  private readonly oneSignalAppId: string;
  private readonly oneSignalApiKey: string;

  private readonly premblyApiKey: string;
  private readonly premblyAppId: string;

  termiiApiKey: string;
  termiiBaseUrl: string;
  termiiConfigId: string;

  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    ){
    this.logger.setContext(IntegrationService.name)

    // if (!this.oneSignalAppId || !this.oneSignalApiKey) {
    //   this.logger.error(`Configuration not found.`);
    //   throw new NotFoundException('OneSignal configuration not found.');
    // }

    // this.premblyAppId = this.configService.get<string>('prembly.appId')
    // this.premblyApiKey = this.configService.get<string>('prembly.apiKey')
    //
    // if (!this.premblyAppId || !this.premblyApiKey) {
    //   this.logger.error(`Configuration not found.`);
    //   throw new NotFoundException('Prembly configuration not found.');
    // }

    this.termiiApiKey = this.configService.get<string>('TERMII_API_KEY');
    this.termiiBaseUrl = this.configService.get<string>('TERMII_BASE_URL');
    this.termiiConfigId = this.configService.get<string>('TERMII_CONFIG_ID');

    if (!this.termiiApiKey) {
      throw new NotFoundException('Termii verification configuration not found.');
    }
  }

  // private async premblyRequestSetup(url: string, payload: object){
  //   try{
  //     const headers = {
  //         accept: 'application/json',
  //         'content-type': 'application/json',
  //         "x-api-key": this.premblyApiKey,
  //         "app_id": this.premblyAppId
  //       }
  //
  //     return await lastValueFrom(
  //       this.httpService.post(
  //         url,
  //         payload,
  //         { headers },
  //       ),
  //     );
  //   } catch (error){
  //     this.logger.error(`Something went wrong. ${error?.message}`);
  //     throw new Error(`Something went wrong. ${error?.message}`);
  //   }
  // }
  //
  // public async validateDriversLisense(number: string, dob: string, firstName: string, lastName: string){
  //   CoreUtils.validateStringParams({ number, dob, firstName, lastName });
  //   return this.premblyRequestSetup('https://api.prembly.com/identitypass/verification/drivers_license', {
  //     number,
  //     dob,
  //     first_name: firstName,
  //     last_name: lastName
  //   });
  // }

  public async sendOtpToPhoneNumber(phoneNumber: string, otp: string, otpTtl: number){
    try {
      const response = await lastValueFrom(
        this.httpService.post(
          'https://api.ng.termii.com/api/sms/otp/send',
          {
            api_key: this.termiiApiKey,
            message_type: 'NUMERIC',
            to: `234${phoneNumber.substring(1)}`,
            from: 'Ogaryde',
            channel: 'dnd',
            pin_attempts: 3,
            pin_time_to_live: otpTtl,
            pin_length: 4,
            pin_placeholder: '< 1234 >',
            message_text: `Your account OTP is: < ${otp} > `,
            pin_type: 'NUMERIC',
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(`OTP sent to phone number ${phoneNumber}`);
      return response.data;
    } catch (error) {
      this.logger.error('Error sending OTP to phone number', error);
      CoreUtils.throwError(error);
    }
  }

  /**
   * Verify if a user exists by phone number in the system.
   * @returns boolean - True if user exists, false otherwise.
   * @param pin
   * @param pinId
   */
  public async verifyOtp(pin: string, pinId: string) {
    try {
      const data = {
        api_key: this.termiiApiKey,
        pin_id: pinId,
        pin: pin,
      };

      const response = await lastValueFrom(
        this.httpService.post(
          `${this.termiiBaseUrl}/api/sms/otp/verify`,
          data,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Error verifying OTP', error);
      CoreUtils.throwError(error);
    }
  }

  /**
   * Send verification OTP to email.
   * @param email - The email of the user.
   * @param otp - The OTP to send.
   */
  public async sendOtpToEmail(email: string, otp: string){
    try {
      const response = await lastValueFrom(
        this.httpService.post(
          'https://api.ng.termii.com/api/email/otp/send',
          {
            api_key: this.termiiApiKey,
            email_configuration_id: this.termiiConfigId,
            email_address: email,
            code: otp
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );
      return response?.data;
    } catch (error){
      this.logger.error('Error sending OTP to email', error);
      CoreUtils.throwError(error)
    }
  }
}
