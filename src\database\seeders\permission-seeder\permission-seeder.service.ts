import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { LoggerService } from "../../../common/logger/logger.service";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { permissions } from "./data";
import { Permission } from "../../../core/authorization/permission/entities/permission.entity";

@Injectable()
export class PermissionSeederService {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Permission)
    private readonly permissionRepo: Repository<Permission>
  ) {
    this.logger.setContext(PermissionSeederService.name)
  }

  async seed(){
    let createdCount: number = 0
    for(const permission of permissions){
      try{
        const existingPermission = await this.permissionRepo.findOne({where: {name: permission.name}})

        if(!existingPermission) {
          const newPermission = this.permissionRepo.create({
            name: permission.name,
            description: permission.description
          });
          await this.permissionRepo.save(newPermission);
          createdCount++;
        }
      } catch (error){
        this.logger.error(`Error creating permission ${permission.name}: ${error}`)
        throw new InternalServerErrorException(`Error creating permission ${permission.name}: ${error}`)
      }
    }
    return createdCount;
  }
}
