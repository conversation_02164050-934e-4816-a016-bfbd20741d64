import { Controller } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { LoggerService } from '@common/logger/logger.service';

@Controller({
  path: 'wallet',
  version: '1',
})
export class WalletController {
  constructor(
    private readonly walletService: WalletService,
    private readonly loggerService: LoggerService,
  ) {
    this.loggerService.setContext(WalletController.name);
  }

}
