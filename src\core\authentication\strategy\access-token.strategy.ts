import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { JwtPayload } from 'jsonwebtoken';

import { ProfileService } from '@core/profile/profile.service';
import { Profile } from '@core/profile/entities/profile.entity';

@Injectable()
export class AccessTokenStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private configService: ConfigService,
    private profileService: ProfileService,
    private readonly i18n: I18nService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('keys.secret'),
    });
  }

  async validate(payload: JwtPayload): Promise<Profile> {
    const { email } = payload;
    const profile = await this.profileService.findProfileByEmail(email);

    if (!profile) {
      throw new NotFoundException(
        this.i18n.t('message.errors.invalid_credentials', { args: { field: 'token' } }),
      );
    }

    if (!profile.verified) {
      throw new UnauthorizedException(
        this.i18n.t('message.errors.not_verified', { args: { entity: 'profile' } }),
      );
    }

    return profile;
  }
}
