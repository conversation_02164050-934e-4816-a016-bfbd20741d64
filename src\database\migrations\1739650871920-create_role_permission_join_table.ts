import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateRolePermissionJoinTable1739650871920 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create join table for ROLE and PERMISSION
    await queryRunner.createTable(
      new Table({
        name: 'role_permission',
        columns: [
          {
            name: 'role_id',
            type: 'bigint',
          },
          {
            name: 'permission_id',
            type: 'bigint',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop the join table
    await queryRunner.dropTable('role_permission');
  }
}
