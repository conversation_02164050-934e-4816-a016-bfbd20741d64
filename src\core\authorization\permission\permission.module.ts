import { Modu<PERSON> } from '@nestjs/common';
import { PermissionService } from './permission.service';
import { PermissionController } from './permission.controller';
import { PermissionMapperService } from './permission.mapper.service';
import { LoggerModule } from '../../../common/logger/logger.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Permission } from './entities/permission.entity';
import { PermissionValidationService } from './permission.validation.service';

@Module({
  controllers: [PermissionController],
  exports: [PermissionService],
  imports: [LoggerModule, TypeOrmModule.forFeature([Permission])],
  providers: [PermissionService, PermissionMapperService, PermissionValidationService],
})
export class PermissionModule {}
