import { MigrationInterface, QueryRunner, TableColumn, TableIndex } from 'typeorm';

export class ModifyAddressTable1749587458529 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('address', 'status');
    await queryRunner.addColumn(
      'address',
      new TableColumn({
        name: 'status',
        type: 'varchar',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('address', 'status');
    await queryRunner.addColumn(
      'address',
      new TableColumn({
        name: 'status',
        type: 'enum',
        enum: ['A', 'I'],
        default: "'A'",
      }),
    );
  }
}
