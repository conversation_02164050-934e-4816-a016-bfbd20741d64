import { AbstractEntity } from '@common/entities/base.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';
import { Order } from '@core/order/entities/order.entity';

@Entity({name: 'order_item'})
export class OrderItem extends AbstractEntity {

  @AutoMap(() => Order)
  @ManyToOne(() => Order, (order) => order.items, { eager: false })
  @JoinColumn({ name: 'cart_id', referencedColumnName: 'id' })
  order: Order;

  @AutoMap(() => Product)
  @OneToOne(() => Product, { eager: true })
  @JoinColumn({ name: 'product_id', referencedColumnName: 'id' })
  product: Product;

  @AutoMap()
  @Column({name: 'quantity', type: 'int'})
  quantity: number;

  @AutoMap()
  @Column({name: 'unit_price', type: 'decimal', precision: 10, scale: 2})
  unitPrice: number;
}
