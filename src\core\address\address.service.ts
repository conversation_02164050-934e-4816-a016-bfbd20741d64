import { Injectable } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';

@Injectable()
export class AddressService implements EntityServiceStrategy<AddressService> {
  constructor(
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(AddressService.name);
  }

  activate(ids: number[]): Promise<void> {
    return Promise.resolve(undefined);
  }

  create(data: AddressService): Promise<AddressService> {
    return Promise.resolve(undefined);
  }

  deactivate(ids: number[]): Promise<void> {
    return Promise.resolve(undefined);
  }

  findByPk(id: number): Promise<AddressService | null> {
    return Promise.resolve(undefined);
  }

  modify(id: number, data: AddressService): Promise<AddressService> {
    return Promise.resolve(undefined);
  }

}
