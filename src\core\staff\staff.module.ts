import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Staff } from './entities/staff.entity';
import { StaffController } from './staff.controller';
import { StaffService } from './staff.service';
import { StaffMapperService } from './staff.mapper.service';

@Module({
  controllers: [StaffController],
  providers: [StaffService, StaffMapperService],
  exports: [StaffService],
  imports: [TypeOrmModule.forFeature([Staff]), LoggerModule],
})
export class StaffModule {}
