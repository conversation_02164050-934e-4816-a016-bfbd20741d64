import { PaginatedResponseDto } from '@common/dto/paginated-response.dto';
import { PaginationDto } from '@common/dto/pagination.dto';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { ProfileValidationService } from '@core/profile/profile.validation.service';
import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager } from 'typeorm';
import { Profile } from './entities/profile.entity';

@Injectable()
export class ProfileService implements EntityServiceStrategy<Profile> {
  constructor(
    private readonly logger: LoggerService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly profileValidation: ProfileValidationService,
  ) {
    this.logger.setContext(ProfileService.name);
  }

  async create(data: Profile): Promise<Profile> {
    data.status = EntityStatus.INACTIVE;
    await this.profileValidation.validate(data, DatabaseAction.CREATE);
    return await this.entityManager.save(data);
  }

  async update(data: Profile): Promise<Profile> {
    await this.profileValidation.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  async findByPk(id: number): Promise<Profile | null> {
    return await this.entityManager.findOneBy(Profile, {
      id,
    });
  }

  async findByEmail(email: string) {
    return await this.entityManager.findOneBy(Profile, {
      email,
    });
  }

  async modify(id: number, data: Profile): Promise<Profile> {
    await this.profileValidation.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  table<R = Profile>(paginationDto: PaginationDto, ...args: Array<string | number>): Promise<PaginatedResponseDto<R>> {
    return Promise.resolve(undefined);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const profile: Profile = await this.entityManager.findOne(Profile, {
          where: { id },
        });
        profile.status = EntityStatus.ACTIVE;
        profile.profileStatus = ProfileStatus.ACTIVE;
        await this.entityManager.save(Profile, profile);
      }),
    );
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const profile: Profile = await this.entityManager.findOne(Profile, {
          where: { id },
        });
        profile.status = EntityStatus.INACTIVE;
        profile.profileStatus = ProfileStatus.DEACTIVATED;
        await this.entityManager.save(Profile, profile);
      }),
    );
  }

  /**
   * Suspend a user account.
   * @param accountId - The ID of the account to suspend.
   */
  async suspendProfile(accountId: number) {
    const profileFound = await this.isProfileExists(accountId);

    if (!profileFound) throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));
    profileFound.profileStatus = ProfileStatus.SUSPENDED;
    profileFound.status = EntityStatus.INACTIVE;
    return await this.entityManager.save(profileFound);
  }

  /**
   * Ban a user account.
   * @param profileId
   */
  async banProfile(profileId: number) {
    const profileFound = await this.isProfileExists(profileId);

    if (!profileFound) throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));

    profileFound.profileStatus = ProfileStatus.BANNED;
    profileFound.status = EntityStatus.INACTIVE;
    return await this.entityManager.save(profileFound);
  }

  /**
   * Check if an account exists.
   * @param profileId - The ID of the account to check.
   */
  isProfileExists(profileId: number) {
    return this.entityManager.findOne(Profile, {
      where: { id: profileId },
    });
  }

  async checkProfileEligibility(profile: Profile): Promise<boolean> {
    if (profile.verified === false) {
      throw new ForbiddenException(this.i18n.t('message.errors.not_verified', { args: { entity: 'Profile' } }));
    }

    if (profile.profileStatus === ProfileStatus.SUSPENDED) {
      throw new ForbiddenException(this.i18n.t('message.errors.profile_suspended'));
    }

    if (profile.profileStatus === ProfileStatus.BANNED) {
      throw new ForbiddenException(this.i18n.t('message.errors.profile_banned'));
    }

    if (profile.profileStatus === ProfileStatus.DEACTIVATED) {
      throw new ForbiddenException(this.i18n.t('message.errors.profile_deactivated'));
    }

    return true;
  }

  async markProfileVerified(email: string) {
    const existingProfile = await this.entityManager.findOneBy(Profile, { email });
    if (!existingProfile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));
    }

    existingProfile.verified = true;
    existingProfile.status = EntityStatus.ACTIVE;
    existingProfile.profileStatus = ProfileStatus.ACTIVE;

    return await this.entityManager.save(existingProfile);
  }

  async findProfileByEmail(email: string): Promise<Profile> {
    return await this.entityManager.findOne(Profile, { where: { email } });
  }
}
