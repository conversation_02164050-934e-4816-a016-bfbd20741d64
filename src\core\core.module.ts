import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';

import { LoggerModule } from '@common/logger/logger.module';
import { CloudinaryService } from '@core/storage/cloudinary/cloudinary.service';
import { JwtAuthGuard } from '@core/authentication/guard/jwt-auth.guard';

import { NotificationModule } from '@core/notification/notification.module';
import { IntegrationModule } from '@core/integration/integration.module';
import { AuthenticationModule } from '@core/authentication/authentication.module';
import { AuthorizationModule } from '@core/authorization/authorization.module';
import { ProfileModule } from '@core/profile/profile.module';
import { StaffModule } from '@core/staff/staff.module';
import { AddressModule } from '@core/address/address.module';
import { WalletModule } from './wallet/wallet.module';
import { CustomerModule } from './customer/customer.module';
import { CategoryModule } from './category/category.module';
import { OrderModule } from './order/order.module';
import { OrderItemModule } from './order_item/order_item.module';
import { ProductModule } from './product/product.module';
import { ReviewModule } from './review/review.module';
import { CartItemModule } from './cart_item/cart_item.module';
import { CartModule } from './cart/cart.module';
import { ShippingModule } from './shipping/shipping.module';
import { InventoryModule } from './inventory/inventory.module';
import { StoreModule } from './store/store.module';
import { StripeModule } from './stripe/stripe.module';
import { OtpModule } from './otp/otp.module';

@Module({
  providers: [
    {
      provide: 'StorageService',
      useClass: CloudinaryService,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    JwtService,
  ],
  imports: [
    LoggerModule,
    NotificationModule,
    IntegrationModule,
    AuthenticationModule,
    AuthorizationModule,
    ProfileModule,
    StaffModule,
    AddressModule,
    WalletModule,
    CustomerModule,
    CategoryModule,
    OrderModule,
    OrderItemModule,
    ProductModule,
    ReviewModule,
    CartItemModule,
    CartModule,
    ShippingModule,
    InventoryModule,
    StoreModule,
    StripeModule.forRootAsync(),
    OtpModule,
  ],
})
export class CoreModule {}
