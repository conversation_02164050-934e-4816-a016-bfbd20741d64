import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

/**
 * Creates the address table
 */
export class CreateAddressTable1747666243577 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'address',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['A', 'I'],
            default: "'A'",
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'latitude',
            type: 'varchar',
          },
          {
            name: 'longitude',
            type: 'varchar',
          },
          {
            name: 'house_number',
            type: 'int',
          },
          {
            name: 'street',
            type: 'text',
          },
          {
            name: 'city',
            type: 'varchar',
          },
          {
            name: 'country',
            type: 'varchar',
          },
          {
            name: 'state',
            type: 'varchar',
          },
          {
            name: 'postal_code',
            type: 'varchar',
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'address',
      new TableIndex({
        name: 'IDX_ADDRESS_LOCATION',
        columnNames: ['latitude', 'longitude'],
      }),
    );

    await queryRunner.createIndex(
      'address',
      new TableIndex({
        name: 'IDX_ADDRESS_POSTAL_CODE',
        columnNames: ['postal_code'],
      }),
    );

    await queryRunner.createIndex(
      'address',
      new TableIndex({
        name: 'IDX_ADDRESS_CITY',
        columnNames: ['city', 'state', 'country'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('address', 'IDX_ADDRESS_LOCATION');
    await queryRunner.dropIndex('address', 'IDX_ADDRESS_POSTAL_CODE');
    await queryRunner.dropIndex('address', 'IDX_ADDRESS_CITY');

    await queryRunner.dropTable('address');
  }
}
