import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { MailService } from '@core/notification/mail/mail.service';
import { LoggerService } from '@common/logger/logger.service';
import { EventName } from '@common/enumerations/event_name.enum';

export interface IEmailPayload {
  recipient: string;
  subject: string;
  templateURL?: string;
  context?: object;
  message?: string;
}

@Injectable()
export class NotificationListenerService {
  constructor(
    private readonly logger: LoggerService,
    private readonly mailService: MailService,
  ) {
    this.logger.setContext(NotificationListenerService.name);
  }

  @OnEvent(EventName.EMAIL_NOTIFICATION, { async: true })
  async handleEmail(payload: IEmailPayload) {
    const { recipient, subject, templateURL, context, message } = payload;

    try {
      if (templateURL && context) {
        await this.mailService.sendSmtpWithTemplate(recipient, subject, templateURL, context);
      } else if (message) {
        await this.mailService.sendSmtpMail(recipient, subject, message);
      } else {
        this.logger.warn('Email payload missing both message and template data.');
        return;
      }

      this.logger.log(`Email sent to ${recipient}`);
    } catch (error) {
      this.logger.error(`Error sending email to ${recipient}: ${error.message}`);
    }
  }
}
