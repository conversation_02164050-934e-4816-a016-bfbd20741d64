import { Injectable, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { UploadApiErrorResponse, UploadApiResponse, v2 as cloudinary } from "cloudinary";
import { ICloudinaryStorageOption } from '@config/storage';
import { LoggerService } from '@common/logger/logger.service';
import * as mime from "mime-types";
import { StorageStrategy } from '@common/pattern/storage.strategy';

@Injectable()
export class CloudinaryService implements StorageStrategy {
  private readonly storageConfig: ICloudinaryStorageOption;

  constructor(
    private readonly logger: LoggerService,
    private configService: ConfigService,
  ) {
    this.logger.setContext(CloudinaryService.name);
    this.storageConfig =
      this.configService.get<ICloudinaryStorageOption>('storage.cloudinary');
    if (this.storageConfig) {
      cloudinary.config({
        cloud_name: this.storageConfig.cloudName,
        api_key: this.storageConfig.key,
        api_secret: this.storageConfig.secret,
      });
      this.logger.log('Cloudinary configuration initialized.');
    } else {
      this.logger.error('Cloudinary configuration not found.');
      throw new NotFoundException('Cloudinary configuration not found.');
    }
  }

  async deleteFile(publicId: string, signature?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      cloudinary.uploader.destroy(publicId, (error, result) =>
        error ? reject(error) : resolve(result),
      );
    });
  }

  async uploadFile(
    file: Buffer,
    resourceType: 'image' | 'video' | 'auto' = 'auto',
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<UploadApiResponse | UploadApiErrorResponse> {
    try {
      // Detect MIME type
      const mimeType =
        mime.lookup(extension || '') || 'application/octet-stream';

      // Set resource type based on mime type (audio -> video, image -> image)
      const detectedResourceType = mimeType.startsWith('image')
        ? 'image'
        : mimeType.startsWith('audio')
        ? 'video'
        : resourceType;

      return await cloudinary.uploader.upload(
        `data:${mimeType};base64,${file.toString('base64')}`,
        {
          folder: folder,
          unique_filename: true,
          resource_type: detectedResourceType,
        },
      );
    } catch (error) {
      this.logger.error('Error in uploadFile method', error);
      throw error;
    }
  }
}
