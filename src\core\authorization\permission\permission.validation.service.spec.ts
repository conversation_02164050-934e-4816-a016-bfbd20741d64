import { Test, TestingModule } from '@nestjs/testing';
import { PermissionValidationService } from './permission.validation.service';

describe('PermissionValidatorService', () => {
  let service: PermissionValidationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PermissionValidationService],
    }).compile();

    service = module.get<PermissionValidationService>(PermissionValidationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
