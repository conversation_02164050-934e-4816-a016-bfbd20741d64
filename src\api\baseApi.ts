import { BadRequestException } from "@nestjs/common";
import { URLSearchParams } from "url";

class BaseApi {
    baseUrl: string;

    constructor(url: string) {
        this.baseUrl = url;
    }

    fetch = async (
        url: string,
        body?: BodyInit, 
        args?: Record<string, any>, 
        requestInit?: RequestInit
    ) => {
      try {
        const urlObj = new URL(url, this.baseUrl);

        if (args) {
            const searchParams = new URLSearchParams(args);
            searchParams.forEach((value, key) => {
              urlObj.searchParams.append(key, value);
            });
        }

        const requestOptions = { ...requestInit, body };

        const response = await fetch(urlObj.toString(), requestOptions);

        if(!response.ok) {
          const errorMessage = await response.text();
          throw new BadRequestException(errorMessage);
        }

        if (response.status === 204) {
          return;
        }

        return response.json();
      } catch (e: any) {
        throw new BadRequestException(e.message);
      }
    };

    get = <T>(
      url: string, 
      args?: Record<string, any>, 
      requestInit?: RequestInit
    ): Promise<T> =>
       this.fetch(url, undefined, args, { ...requestInit, method: 'GET'});

    post = <T>(
      url: string,
      body?: Record<string, any>,
      args?: Record<string, any>,
      requestInit?: RequestInit  
    ): Promise<T> => {
      const bodyString = body ? JSON.stringify(body) : undefined;
      
      return this.fetch(url, bodyString, args, {
        ...requestInit,
        method: 'POST',
      });
    };   
}

export default BaseApi;